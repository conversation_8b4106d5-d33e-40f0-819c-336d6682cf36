package com.dangbei.aisearch.infrastructure.gatewayimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dangbei.aisearch.client.enums.KnowledgeApplyStatusEnum;
import com.dangbei.aisearch.domain.entity.KnowledgeApplyEntity;
import com.dangbei.aisearch.domain.gateway.KnowledgeApplyGateway;
import com.dangbei.aisearch.infrastructure.common.base.BaseGatewayImpl;
import com.dangbei.aisearch.infrastructure.convertor.KnowledgeApplyConvertor;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.dataobject.KnowledgeApplyDO;
import com.dangbei.aisearch.infrastructure.gatewayimpl.database.mapper.KnowledgeApplyMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * KnowledgeApply 网关层实现类
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-27
 */
@Component
public class KnowledgeApplyGatewayImpl extends BaseGatewayImpl<Long, KnowledgeApplyEntity, KnowledgeApplyDO, KnowledgeApplyMapper, KnowledgeApplyConvertor> implements KnowledgeApplyGateway {

    @Override
    public int removeByKnowledgeId(String knowledgeId) {
        if (StringUtils.isBlank(knowledgeId)) {
            return 0;
        }

        LambdaUpdateWrapper<KnowledgeApplyDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(KnowledgeApplyDO::getKnowledgeId, knowledgeId);
        updateWrapper.eq(KnowledgeApplyDO::getIsDeleted, 0L);
        updateWrapper.set(KnowledgeApplyDO::getIsDeleted, System.currentTimeMillis());

        return baseMapper.update(null, updateWrapper);
    }

    @Override
    public KnowledgeApplyEntity getPendingApply(String knowledgeId, String applicantId) {
        if (StringUtils.isAnyBlank(knowledgeId, applicantId)) {
            return null;
        }

        LambdaQueryWrapper<KnowledgeApplyDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KnowledgeApplyDO::getKnowledgeId, knowledgeId);
        queryWrapper.eq(KnowledgeApplyDO::getApplicantId, applicantId);
        queryWrapper.eq(KnowledgeApplyDO::getStatus, KnowledgeApplyStatusEnum.PENDING.getCode());
        queryWrapper.last(LIMIT_ONE);

        KnowledgeApplyDO applyDO = baseMapper.selectOne(queryWrapper);
        return applyDO != null ? convertor.toEntity(applyDO) : null;
    }

    @Override
    public KnowledgeApplyEntity getByApplyId(String applyId) {
        if (StringUtils.isBlank(applyId)) {
            return null;
        }
        LambdaQueryWrapper<KnowledgeApplyDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KnowledgeApplyDO::getApplyId, applyId);
        queryWrapper.last(LIMIT_ONE);

        KnowledgeApplyDO applyDO = baseMapper.selectOne(queryWrapper);
        return applyDO != null ? convertor.toEntity(applyDO) : null;
    }

    @Override
    public List<KnowledgeApplyEntity> listApplies(String knowledgeId, KnowledgeApplyStatusEnum status, int offset, int limit) {
        if (StringUtils.isBlank(knowledgeId)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<KnowledgeApplyDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KnowledgeApplyDO::getKnowledgeId, knowledgeId);
        if (Objects.nonNull(status)) {
            queryWrapper.eq(KnowledgeApplyDO::getStatus, status.getCode());
        }
        queryWrapper.orderByDesc(KnowledgeApplyDO::getCreateTime);
        queryWrapper.last("LIMIT " + offset + "," + limit);

        List<KnowledgeApplyDO> applyDOList = baseMapper.selectList(queryWrapper);
        return convertor.toEntityList(applyDOList);
    }

    @Override
    public long countApplies(String knowledgeId, KnowledgeApplyStatusEnum status) {
        if (StringUtils.isBlank(knowledgeId)) {
            return 0;
        }

        LambdaQueryWrapper<KnowledgeApplyDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KnowledgeApplyDO::getKnowledgeId, knowledgeId);
        if (Objects.nonNull(status)) {
            queryWrapper.eq(KnowledgeApplyDO::getStatus, status.getCode());
        }

        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public List<KnowledgeApplyEntity> listUserApplies(String applicantId, KnowledgeApplyStatusEnum status, int offset, int limit) {
        if (StringUtils.isBlank(applicantId)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<KnowledgeApplyDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KnowledgeApplyDO::getApplicantId, applicantId);
        if (Objects.nonNull(status)) {
            queryWrapper.eq(KnowledgeApplyDO::getStatus, status.getCode());
        }
        queryWrapper.orderByDesc(KnowledgeApplyDO::getCreateTime);
        queryWrapper.last("LIMIT " + offset + "," + limit);

        List<KnowledgeApplyDO> applyDOList = baseMapper.selectList(queryWrapper);
        return convertor.toEntityList(applyDOList);
    }

    @Override
    public long countUserApplies(String applicantId, KnowledgeApplyStatusEnum status) {
        if (StringUtils.isBlank(applicantId)) {
            return 0;
        }

        LambdaQueryWrapper<KnowledgeApplyDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KnowledgeApplyDO::getApplicantId, applicantId);
        if (Objects.nonNull(status)) {
            queryWrapper.eq(KnowledgeApplyDO::getStatus, status.getCode());
        }

        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public List<KnowledgeApplyEntity> listUserAppliesForKnowledge(String knowledgeId, String applicantId) {
        if (StringUtils.isAnyBlank(knowledgeId, applicantId)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<KnowledgeApplyDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KnowledgeApplyDO::getKnowledgeId, knowledgeId);
        queryWrapper.eq(KnowledgeApplyDO::getApplicantId, applicantId);
        queryWrapper.orderByDesc(KnowledgeApplyDO::getCreateTime);

        List<KnowledgeApplyDO> applyDOList = baseMapper.selectList(queryWrapper);
        return convertor.toEntityList(applyDOList);
    }

    @Override
    public Page<KnowledgeApplyEntity> pageApplies(String knowledgeId, KnowledgeApplyStatusEnum status, long pageIndex, long pageSize) {
        if (StringUtils.isBlank(knowledgeId)) {
            return new Page<>(pageIndex, pageSize, 0);
        }

        // 构建查询条件
        LambdaQueryWrapper<KnowledgeApplyDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KnowledgeApplyDO::getKnowledgeId, knowledgeId);
        if (Objects.nonNull(status)) {
            queryWrapper.eq(KnowledgeApplyDO::getStatus, status.getCode());
        }
        queryWrapper.orderByDesc(KnowledgeApplyDO::getCreateTime);

        // 执行分页查询
        Page<KnowledgeApplyDO> page = new Page<>(pageIndex, pageSize);
        Page<KnowledgeApplyDO> applyPage = baseMapper.selectPage(page, queryWrapper);

        // 转换为Entity分页结果
        Page<KnowledgeApplyEntity> entityPage = new Page<>(pageIndex, pageSize, applyPage.getTotal());
        entityPage.setRecords(convertor.toEntityList(applyPage.getRecords()));

        return entityPage;
    }
}
