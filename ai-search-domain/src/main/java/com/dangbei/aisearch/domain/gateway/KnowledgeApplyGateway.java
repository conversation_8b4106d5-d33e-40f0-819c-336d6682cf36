package com.dangbei.aisearch.domain.gateway;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dangbei.aisearch.client.enums.KnowledgeApplyStatusEnum;
import com.dangbei.aisearch.domain.common.base.BaseGateway;
import com.dangbei.aisearch.domain.entity.KnowledgeApplyEntity;

import java.util.List;

/**
 * KnowledgeApply 网关层
 * <AUTHOR> href="<EMAIL>"><EMAIL></a>
 * @version 1.0.0
 * @since 2025-05-27
 */
public interface KnowledgeApplyGateway extends BaseGateway<Long, KnowledgeApplyEntity> {

    /**
     * 根据知识库ID批量删除申请记录
     * @param knowledgeId 知识库ID
     * @return 删除的记录数
     */
    int removeByKnowledgeId(String knowledgeId);
    
    /**
     * 查询用户对知识库的待处理申请
     * @param knowledgeId 知识库ID
     * @param applicantId 申请人ID
     * @return 申请实体，如果不存在则返回null
     */
    KnowledgeApplyEntity getPendingApply(String knowledgeId, String applicantId);
    
    /**
     * 根据申请ID查询申请记录
     * @param applyId 申请ID
     * @return 申请实体，如果不存在则返回null
     */
    KnowledgeApplyEntity getByApplyId(String applyId);
    
    /**
     * 查询知识库的申请列表
     * @param knowledgeId 知识库ID
     * @param status 申请状态，可为null表示查询所有状态
     * @param offset 偏移量
     * @param limit 查询数量
     * @return 申请列表
     */
    List<KnowledgeApplyEntity> listApplies(String knowledgeId, KnowledgeApplyStatusEnum status, int offset, int limit);
    
    /**
     * 查询知识库的申请总数
     * @param knowledgeId 知识库ID
     * @param status 申请状态，可为null表示查询所有状态
     * @return 申请总数
     */
    long countApplies(String knowledgeId, KnowledgeApplyStatusEnum status);
    
    /**
     * 查询用户的申请列表
     * @param applicantId 申请人ID
     * @param status 申请状态，可为null表示查询所有状态
     * @param offset 偏移量
     * @param limit 查询数量
     * @return 申请列表
     */
    List<KnowledgeApplyEntity> listUserApplies(String applicantId, KnowledgeApplyStatusEnum status, int offset, int limit);
    
    /**
     * 查询用户的申请总数
     * @param applicantId 申请人ID
     * @param status 申请状态，可为null表示查询所有状态
     * @return 申请总数
     */
    long countUserApplies(String applicantId, KnowledgeApplyStatusEnum status);
    
    /**
     * 查询用户对特定知识库的所有申请记录
     * @param knowledgeId 知识库ID
     * @param applicantId 申请人ID
     * @return 申请列表
     */
    List<KnowledgeApplyEntity> listUserAppliesForKnowledge(String knowledgeId, String applicantId);
}
