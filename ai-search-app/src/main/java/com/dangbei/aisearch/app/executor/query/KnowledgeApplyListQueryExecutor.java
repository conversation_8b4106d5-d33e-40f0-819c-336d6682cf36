package com.dangbei.aisearch.app.executor.query;

import com.alibaba.cola.dto.PageSingleResponse;
import com.alibaba.cola.exception.Assert;
import com.dangbei.aisearch.app.util.UserContextUtil;
import com.dangbei.aisearch.client.dto.clientobject.KnowledgeApplyCo;
import com.dangbei.aisearch.client.dto.cmd.query.KnowledgeApplyListQuery;
import com.dangbei.aisearch.client.enums.KnowledgeApplyStatusEnum;
import com.dangbei.aisearch.client.enums.SharedKnowledgePermissionEnum;
import com.dangbei.aisearch.domain.entity.KnowledgeApplyEntity;
import com.dangbei.aisearch.domain.entity.SharedKnowledgeEntity;
import com.dangbei.aisearch.domain.entity.UserInfoEntity;
import com.dangbei.aisearch.domain.gateway.KnowledgeApplyGateway;
import com.dangbei.aisearch.domain.gateway.SharedKnowledgeGateway;
import com.dangbei.aisearch.domain.gateway.UserInfoGateway;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 知识库申请列表查询执行器
 * <AUTHOR>
 * @date 2025-05-27
 **/
@Slf4j
@Component
public class KnowledgeApplyListQueryExecutor {

    @Resource
    private KnowledgeApplyGateway knowledgeApplyGateway;
    @Resource
    private SharedKnowledgeGateway sharedKnowledgeGateway;
    @Resource
    private UserInfoGateway userInfoGateway;

    /**
     * 执行查询
     * @param query 查询条件
     * @return 申请列表分页数据
     */
    public PageSingleResponse<KnowledgeApplyCo> execute(KnowledgeApplyListQuery query) {
        // 获取当前用户
        String userId = UserContextUtil.getNonNullUserId();
        // 获取知识库
        SharedKnowledgeEntity knowledge = sharedKnowledgeGateway.getByKnowledgeId(query.getKnowledgeId());
        Assert.notNull(knowledge, "知识库不存在");
        // 验证权限
        knowledge.validatePermission(userId, SharedKnowledgePermissionEnum.APPROVE_APPLY);
        // 获取状态枚举
        KnowledgeApplyStatusEnum statusEnum = null;
        if (Objects.nonNull(query.getStatus()) && KnowledgeApplyStatusEnum.isValidCode(query.getStatus())) {
            statusEnum = KnowledgeApplyStatusEnum.getByCode(query.getStatus());
        }
        // 分页查询
        int offset = (query.getPageIndex() - 1) * query.getPageSize();
        List<KnowledgeApplyEntity> applyList = knowledgeApplyGateway.listApplies(
            query.getKnowledgeId(), statusEnum, offset, query.getPageSize()
        );
        long total = knowledgeApplyGateway.countApplies(query.getKnowledgeId(), statusEnum);
        // 转换为客户端对象
        List<KnowledgeApplyCo> coList = convertToCos(applyList, knowledge, userId, query.getKeyword());
        // 构建分页响应
        return PageSingleResponse.of(coList, total, query.getPageSize(), query.getPageIndex());
    }

    /**
     * 转换为客户端对象
     * @param applyList 申请列表
     * @param knowledge 知识库实体
     * @param userId    当前用户ID
     * @param keyword   搜索关键词
     * @return 客户端对象列表
     */
    private List<KnowledgeApplyCo> convertToCos(
        List<KnowledgeApplyEntity> applyList,
        SharedKnowledgeEntity knowledge,
        String userId,
        String keyword) {
        if (applyList == null || applyList.isEmpty()) {
            return new ArrayList<>();
        }

        return applyList.stream()
            .map(apply -> convertToCo(apply, knowledge, userId, keyword))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * 转换单个申请为客户端对象
     * @param apply     申请实体
     * @param knowledge 知识库实体
     * @param userId    当前用户ID
     * @param keyword   搜索关键词
     * @return 客户端对象
     */
    private KnowledgeApplyCo convertToCo(
        KnowledgeApplyEntity apply,
        SharedKnowledgeEntity knowledge,
        String userId,
        String keyword) {
        // 获取申请人信息
        UserInfoEntity applicant = apply.getApplicantInfo();
        if (Objects.isNull(applicant)) {
            return null;
        }

        // 关键词过滤
        if (StringUtils.isNotBlank(keyword) &&
            !StringUtils.containsIgnoreCase(applicant.getNickname(), keyword)) {
            return null;
        }

        // 获取审批人信息
        UserInfoEntity approver = null;
        if (StringUtils.isNotBlank(apply.getApproverId())) {
            approver = userInfoGateway.getByUserId(apply.getApproverId());
        }

        // 构建客户端对象
        return KnowledgeApplyCo.builder()
            .applyId(apply.getApplyId())
            .knowledgeId(apply.getKnowledgeId())
            .knowledgeName(knowledge.getName())
            .applicantUserId(apply.getApplicantId())
            .applicantNickname(applicant.getNickname())
            .applicantAvatar(applicant.getAvatar())
            .applyReason(apply.getRemark())
            .status(apply.getStatus())
            .statusName(apply.getStatusEnum().getDesc())
            .applyTime(apply.getApplyTime() != null ? apply.getApplyTime().toString() : null)
            .approverUserId(apply.getApproverId())
            .approverNickname(approver != null ? approver.getNickname() : null)
            .approveTime(apply.getProcessTime() != null ? apply.getProcessTime().toString() : null)
            .remark(apply.getRemark())
            .canApprove(apply.isPending() && knowledge.checkPermission(userId, SharedKnowledgePermissionEnum.APPROVE_APPLY))
            .canCancel(apply.isPending() && userId.equals(apply.getApplicantId()))
            .build();
    }
}
